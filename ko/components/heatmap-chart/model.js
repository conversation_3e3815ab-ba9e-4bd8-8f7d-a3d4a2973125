import { FComponent } from "../f-component";
import HighchartsModern from "@/utils/highcharts-modern";
import { declOfNum } from "@/utils/string/decl-of-num";

let uniqueId = 1;

export class ViewModel extends FComponent {
  constructor(params, element) {
    super(params, element);

    this.id = uniqueId++;
    this.chartContainerId = `heatmap-chart-${this.id}`;

    // Observables for component state
    this.loading = ko.observable(false);
    this.error = ko.observable(null);
    this.chartInitialized = ko.observable(false);
    this.options = params.options || {};

    console.log('[HEATMAP CHART] options', this.options);

    // Parameters from parent
    this.imageSrc = ko.isObservable(params.imageSrc)
      ? params.imageSrc
      : ko.observable(params.imageSrc);
    this.data = ko.isObservable(params.data) ? params.data : ko.observable(params.data || []);
    this.gridWidth = ko.isObservable(this.options.gridWidth) ? this.options.gridWidth : ko.observable(this.options.gridWidth || 10);
    this.gridHeight = ko.isObservable(this.options.gridHeight) ? this.options.gridHeight : ko.observable(this.options.gridHeight || 10);

    console.log('[HEATMAP CHART] data', this.data());
    console.log('[HEATMAP CHART] gridWidth', this.gridWidth());
    console.log('[HEATMAP CHART] gridHeight', this.gridHeight());

    this.onPointClick = params.onPointClick || (() => {});

    // Chart instance
    this.chart = null;
    this.resizeObserver = null;
    this.currentDimensions = { width: 0, height: 0 };

    // Image loading state
    this.imageLoaded = ko.observable(false);
    this.imageError = ko.observable(false);

    this.addSubscription(
      this.data.subscribe((newData) => {
        if (this.chart && this.chartInitialized()) {
          this.updateChartData(newData);
        }
      })
    );

    this.addSubscription(
      this.imageSrc.subscribe((newSrc) => {
        if (newSrc) {
          this.imageLoaded(false);
          this.imageError(false);
          this.chartInitialized(false);
        }
      })
    );
  }

  onImageLoad(data, event) {
    console.log("[HEATMAP CHART] Image loaded successfully");
    const img = event.target;

    this.imageLoaded(true);
    this.imageError(false);
    this.error(null);

    setTimeout(() => {
      this.initializeChart(img);
    }, 50);
  }

  onImageError() {
    console.error("[HEATMAP CHART] Failed to load image:", this.imageSrc());
    this.imageError(true);
    this.imageLoaded(false);
    this.error("Не удалось загрузить изображение");
  }

  initializeChart(img) {
    const container = document.getElementById(this.chartContainerId);
    if (!container) {
      console.error("[HEATMAP CHART] Container not found:", this.chartContainerId);
      return;
    }

    const imageElement = this.element.querySelector(".heatmap-chart__background-image");
    if (!imageElement) {
      console.error("[HEATMAP CHART] Image element not found");
      return;
    }

    const containerWidth = imageElement.offsetWidth;
    const containerHeight = imageElement.offsetHeight;

    console.log("[HEATMAP CHART] Container dimensions:", containerWidth, "x", containerHeight);

    this.currentDimensions = { width: containerWidth, height: containerHeight };
    this.createChart(containerWidth, containerHeight);
    this.setupResizeObserver();
  }

  createChart(width, height) {
    const heatmapData = this.data();
    console.log("heatmapData", heatmapData);
    console.log("[HEATMAP CHART] Creating chart with data points:", heatmapData.length);

    const maxValue = Math.max(1, ...heatmapData.map((d) => d.value));

    const defaultOptions = {
      chart: {
        height: "100%",
        type: "heatmap",
        backgroundColor: "transparent",
        plotBorderWidth: 0,
        margin: [0, 0, 0, 0],
      },
      title: {
        text: undefined,
      },
      credits: {
        enabled: false,
      },
      legend: {
        enabled: false,
      },
      yAxis: {
        visible: false,
        title: {
          text: undefined,
        },
        endOnTick: false,
        minPadding: 0,
        maxPadding: 0,
        min: 0,
        max: ko.unwrap(this.gridHeight),
        tickInterval: 1,
        reversed: true,
      },
      xAxis: {
        visible: false,
        margin: 0,
        minPadding: 0,
        maxPadding: 0,
        min: 0,
        max: ko.unwrap(this.gridWidth),
        tickInterval: 1,
        endOnTick: false,
      },
      colorAxis: {
        // min: 0,
        // max: maxValue,
        stops: [
          [0, "rgba(0, 0, 0, 0)"],
          [0.1, "rgba(0, 100, 255, 0.3)"],
          [0.3, "rgba(0, 200, 255, 0.5)"],
          [0.5, "rgba(100, 255, 100, 0.7)"],
          [0.7, "rgba(255, 200, 0, 0.8)"],
          [1.0, "rgba(255, 0, 0, 0.9)"],
        ],
      },
      tooltip: {
        formatter: function () {
          const clicks = this.point.value;
          if (clicks === 0) {
            return false;
          }
          const clicksWord = declOfNum(clicks, ["клик", "клика", "кликов"]);
          return `<div class="heatmap-chart__tooltip">
              <span class="heatmap-chart__tooltip-text">${clicks} ${clicksWord}</span>
            </div>`;
        },
        useHTML: true,
        shadow: false,
        borderWidth: 0,
        backgroundColor: "transparent",
        hideDelay: 100,
      },
      series: [
        {
          type: "heatmap",
          data: heatmapData,
          interpolation: true,
          colsize: 1,
          rowsize: 1,
          borderWidth: 0,
        },
      ],
    };

    const chartOptions = this.mergeOptions(defaultOptions, this.options);
    console.log("chartOptions", chartOptions);
    this.chart = HighchartsModern.chart(this.chartContainerId, chartOptions);
    this.chartInitialized(true);

    console.log("[HEATMAP CHART] Chart created successfully");
  }

  updateChartData(newData) {
    if (!this.chart) return;

    // const processedData = newData;
    // console.log("[HEATMAP CHART] Updating chart with new data:", processedData.length, "points");

    // this.chart.series[0].setData(processedData, true);

    // const maxValue = Math.max(1, ...processedData.map((d) => d.value));
    // this.chart.colorAxis[0].update({ max: maxValue });
  }

  setupResizeObserver() {
    const imageElement = this.element.querySelector(".heatmap-chart__background-image");
    if (!imageElement || this.resizeObserver) return;

    this.resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width !== this.currentDimensions.width && width > 0) {
          this.handleResize(width, height);
        }
      }
    });

    this.resizeObserver.observe(imageElement);
  }

  handleResize(newWidth, newHeight) {
    if (!this.chart || !this.chartInitialized()) return;

    if (Math.abs(newWidth - this.currentDimensions.width) > 10) {
      console.log("[HEATMAP CHART] Resizing chart to:", newWidth, "x", newHeight);

      this.currentDimensions = { width: newWidth, height: newHeight };

      this.chart.setSize(newWidth, newHeight, false);
      this.chart.redraw();
    }
  }

  mergeOptions(defaultOptions, customOptions) {
    // Deep merge function for chart options
    const merge = (target, source) => {
      for (const key in source) {
        if (source[key] && typeof source[key] === "object" && !Array.isArray(source[key])) {
          target[key] = target[key] || {};
          merge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
      return target;
    };

    return merge({ ...defaultOptions }, customOptions);
  }

  dispose() {
    console.log("[HEATMAP CHART] Disposing component");

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }

    super.dispose();
  }
}
