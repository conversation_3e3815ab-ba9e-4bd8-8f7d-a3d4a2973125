.heatmap-chart {
  &__container {
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  &__background-image {
    width: 100%;
    height: auto;
    display: block;
    max-width: 100%;
  }

  &__chart-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    
    .highcharts-container {
      width: 100% !important;
      height: 100% !important;
    }
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }

  &__loading-spinner {
    text-align: center;
    color: #666;
    font-size: 14px;
  }

  &__error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff6b6b;
    padding: 20px;
    border-radius: 8px;
    z-index: 10;
  }

  &__error-message {
    color: #d63031;
    font-size: 14px;
    text-align: center;
  }

  &__tooltip {
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #eee;
  }

  &__tooltip-text {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}