<div class="heatmap-chart__container" data-bind="style: { position: 'relative' }">
  <!-- Background image -->
  <img 
    class="heatmap-chart__background-image" 
    data-bind="
      attr: { src: imageSrc }, 
      visible: imageSrc,
      event: { load: onImageLoad.bind($data), error: onImageError.bind($data) }
    "
   style="height: auto; display: block;"
  />
  
  <!-- Chart container positioned absolutely over the image -->
  <div 
    class="heatmap-chart__chart-container" 
    data-bind="
      attr: { id: chartContainerId },
      style: { 
        position: 'absolute', 
        top: 0, 
        left: 0, 
        width: '100%', 
        height: '100%',
        pointerEvents: 'auto'
      },
      visible: chartInitialized
    "
  ></div>
  
  <!-- Loading state -->
  <!-- ko if: loading -->
  <div class="heatmap-chart__loading">
    <div class="heatmap-chart__loading-spinner">Загрузка...</div>
  </div>
  <!-- /ko -->
  
  <!-- Error state -->
  <!-- ko if: error -->
  <div class="heatmap-chart__error">
    <div class="heatmap-chart__error-message" data-bind="text: error"></div>
  </div>
  <!-- /ko -->
</div> 