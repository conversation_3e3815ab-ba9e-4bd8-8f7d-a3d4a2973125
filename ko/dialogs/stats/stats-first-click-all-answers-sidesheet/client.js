import { Client } from '../client';

export class FirstClickAnswersClient extends Client {
  constructor(data) {
    super({
      name: data.name,
      email: data.email,
      phone: data.phone,
      passedAt: data.passedAt,
      orderId: data.orderId,
      orderCreatedAt: data.orderCreatedAt,
      comment: data.comment,
      points: data.points,
      filialName: data.filialName,
      skipped: data.skipped,
      timeout: data.timeout,
      without_points: data.without_points
    });

    console.log('FirstClickAnswersClient data', data);

    this.clicks = [];
    this.clicksByArea = []
    this.userDefinedPoints = []
    if (data.answer && typeof data.answer === 'object') {
      let clickIndex = 1;
      Object.entries(data.answer).forEach(([areaId, areaData]) => {
        if (areaData.points && Array.isArray(areaData.points)) {

          if (areaId !== '0' && areaData.points.length > 0) {
            this.clicksByArea.push({
              areaId: areaId,
              areaName: areaData.name,
              points: [],
              totalSeconds: 0,
            });
          }

          areaData.points.forEach((click, index) => {
            const {x, y, t} = click;
            if (areaId === '0') {
              this.userDefinedPoints.push({
                x,
                y,
                timeInSeconds: t,
                name: `Пользовательская точка ${index + 1}`
              });
            } else {
              const area = this.clicksByArea.find(area => area.areaId === areaId);
              area.totalSeconds += t;
              area.points.push({
                x,
                y,
                timeInSeconds: t
              });
            }

            this.clicks.push({
              x,
              y,
              timeInSeconds: t,
              label: `${areaData.name} - точка ${clickIndex}`,
              areaId: areaId,
              areaName: areaData.name
            });
            clickIndex++;
          });
        }
      });
    }
    
    this.timeToFirstClick = data.time_to_first_click;
    this.skipped = data.skipped;
    this.hasPoints = this.clicksByArea.length > 0 || this.userDefinedPoints.length > 0;

    console.log('FirstClickAnswersClient clicks', this.clicks);
    console.log('FirstClickAnswersClient clicksByArea', this.clicksByArea);
    console.log('FirstClickAnswersClient userDefinedPoints', this.userDefinedPoints);
    console.log('FirstClickAnswersClient skipped', this.skipped);
  }
}
