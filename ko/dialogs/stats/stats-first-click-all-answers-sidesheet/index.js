import { StatsDialog } from '../dialog';
import html from './template.html';
import './style.less';
import { Table } from './table';
import { useMediaQuery } from '@/hooks/useMediaQuery';

class StatsFirstClickAllAnswersSidesheetModel extends StatsDialog {
  constructor(params, element) {
    super(params, element);
    this.filterParams = params.filterParams || {};
    this.view = params.view || 'default';
    this.isTablet = ko.observable(false);
    this.disposeMediaQuery = useMediaQuery(
      '(max-width: 1199px)',
      this.isTablet
    );
    console.log('[FIRST CLICK TEST] StatsFirstClickAllAnswersSidesheetModel constructor', this.view);
  }

  dispose() {
    if (this.disposeMediaQuery) {
      this.disposeMediaQuery();
    }
  }

  createTable() {
    return new Table();
  }

  processAnswerData(answer) {
    return {
      ...answer,
      respondentName: answer.respondent_name || `Респондент ${answer.respondent_id || answer.id}`,
      answerTime: answer.time_to_first_click !== null ? `${answer.time_to_first_click} сек` : 'N/A',
      status: this.getAnswerStatus(answer),
      clickPoints: (answer.click_points || []).map(point => ({
        ...point,
        areaName: this.getAreaNameById(point.area_id) || 'Вне области',
        timeFormatted: point.time !== null ? `${point.time} сек` : 'N/A'
      })),
      commentText: answer.comment || null,
      mapId: ko.utils.uniqueId('answer-map-')
    };
  }

  getAreaNameById(areaId) {
    if (!areaId || !this.question.processedAreas) return null;
    const area = this.question.processedAreas.find(a => a.id === areaId);
    return area ? area.name : null;
  }

  getAnswerStatus(answer) {
    if (answer.skipped) return 'Пропущен';
    if (answer.timeout) return 'Время вышло';
    if (answer.click_points && answer.click_points.length > 0) return 'Ответил';
    return 'Нет данных';
  }
  
  showClickOnMap(answer, clickPoint) {
    console.log('showClickOnMap in StatsFirstClickAllAnswersSidesheetModel', answer, clickPoint);
  }

  openClicksMapSidesheet(answerData = null) {
    console.log('openClicksMapSidesheet in StatsFirstClickAllAnswersSidesheetModel');
    console.log('openClicksMapSidesheet table', this.table);
    console.log('openClicksMapSidesheet answerData', answerData);
    console.log('openClicksMapSidesheet question', this.question);
    this.question.openUserClickMapSidesheet(answerData.clicks);
  }
}

ko.components.register('stats-first-click-all-answers-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-first-click-all-answers-sidesheet-wrapper'); 
      return new StatsFirstClickAllAnswersSidesheetModel(params, element);
    },
  },
  template: html
});

export default StatsFirstClickAllAnswersSidesheetModel;
