<stats-sidesheet
  class="first-click-answers-stats-sidesheet"
  params="ref: modal,
            dialogWrapper: $component,
            title: title,
            question: question,
            withPoints: false"
>
  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table
        class="table foq-table question-statistics__clients-modal-dialog-table first-click-answers-stats-sidesheet__table"
      >
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Филиал</th>
            <th>Комментарий</th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td
              class="question-statistics__clients-modal-dialog-table-phone-cell"
              data-bind="html: $modal.getText(phone)"
            ></td>
            <td
              class="question-statistics__clients-modal-dialog-table-email-cell"
              data-bind="html: $modal.getText(email)"
            ></td>
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a
                class="question-statistics__clients-modal-dialog-table-link"
                data-bind="html: '#' + $modal.getText(orderId), attr: { href: $modal.getOrderLink(orderId) }"
              >
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(filialName)"></td>
            <td data-bind="html: $modal.getText(comment)"></td>
          </tr>

          <!-- ko if: $data.skipped && $modal.view !== 'skipped' -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row first-click-answers-stats-sidesheet__skipped-row"
          >
            <td colspan="99">Отказался от ответа</td>
          </tr>
          <!-- /ko -->

          <!-- ko if: (!$data.skipped && $data.timeout) && $modal.view !== 'skipped' -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row first-click-answers-stats-sidesheet__timeout-row"
          >
            <td class="f-fs-1 f-color-critics" colspan="99">
              Респондент не ответил в заданное время
            </td>
          </tr>
          <!-- /ko -->

          <!-- ko if: !$data.skipped && !$data.timeout -->
          <!-- ko if: (!$data.clicks || $data.clicks.length === 0) && $modal.view !== 'skipped' -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row first-click-answers-stats-sidesheet__no-clicks-row"
          >
            <td colspan="99">Респондент не отметил точек</td>
          </tr>
          <!-- /ko -->
          <!-- ko if: $data.clicksByArea && $data.clicksByArea.length > 0 -->
          <!-- ko foreach: { data: $data.clicksByArea, as: '$clickItem' } -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row first-click-answers-stats-sidesheet__clicks-row"
          >
            <td data-bind="attr: { colspan: $modal.isTablet() ? 4 : 3 }" class="semibold">
              <span data-bind="text: $clickItem.areaName"></span>
            </td>
            <td data-bind="text: $clickItem.points.length + ' с', attr: { colspan: $modal.isTablet() ? 4 : 3 }"></td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- ko if: $data.clicks && $data.clicks.length > 0 -->
          <tr
            class="question-statistics__clients-modal-dialog-table-row first-click-answers-stats-sidesheet__map-row"
          >
            <td colspan="99">
              <a
                href="#"
                class="f-color-primary f-fs-1 semibold"
                data-bind="click: function() { $modal.openClicksMapSidesheet($data); }"
                >Карта кликов</a
              >
            </td>
          </tr>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="first-click-answers-stats-sidesheet-mobile-table">
      <!-- ko foreach: $table.items -->
      <div class="first-click-answers-stats-sidesheet-mobile-table__client-card">
        <div class="first-click-answers-stats-sidesheet-mobile-table__client-info">
          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">
              ФИО клиента
            </div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(name)"
            ></div>
          </div>

          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">Телефон</div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(phone)"
            ></div>
          </div>

          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">Email</div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(email)"
            ></div>
          </div>

          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">Пройден</div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(passedAt)"
            ></div>
          </div>

          <!-- ko if: $modal.hasOrder -->
          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">
              № заказа
            </div>
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-value">
              <a
                class="first-click-answers-stats-sidesheet-mobile-table__link"
                data-bind="html: '#' + $modal.getText(orderId), attr: { href: $modal.getOrderLink(orderId) }"
              ></a>
            </div>
          </div>

          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">
              Дата заказа
            </div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(orderCreatedAt)"
            ></div>
          </div>
          <!-- /ko -->

          <div class="first-click-answers-stats-sidesheet-mobile-table__field">
            <div class="first-click-answers-stats-sidesheet-mobile-table__field-label">Филиал</div>
            <div
              class="first-click-answers-stats-sidesheet-mobile-table__field-value"
              data-bind="html: $modal.getText(filialName)"
            ></div>
          </div>
        </div>

        <div class="first-click-answers-stats-sidesheet-mobile-table__comment-section">
          <div class="first-click-answers-stats-sidesheet-mobile-table__comment-label">
            Комментарий
          </div>
          <div
            class="first-click-answers-stats-sidesheet-mobile-table__comment-value"
            data-bind="html: $modal.getText(comment)"
          ></div>
        </div>

        <div class="first-click-answers-stats-sidesheet-mobile-table__clicks-section">
          <!-- ko if: $data.skipped -->
          <div
            class="first-click-answers-stats-sidesheet-mobile-table__status-message first-click-answers-stats-sidesheet-mobile-table__status-message--skipped"
          >
            Отказался от ответа
          </div>
          <!-- /ko -->

          <!-- ko if: !$data.skipped && $data.timeout -->
          <div
            class="first-click-answers-stats-sidesheet-mobile-table__status-message first-click-answers-stats-sidesheet-mobile-table__status-message--timeout"
          >
            Респондент не ответил в заданное время
          </div>
          <!-- /ko -->

          <!-- ko if: !$data.skipped && !$data.timeout -->
          <!-- ko if: !$data.hasPoints -->
          <div
            class="first-click-answers-stats-sidesheet-mobile-table__status-message first-click-answers-stats-sidesheet-mobile-table__status-message--no-clicks"
          >
            Респондент не отметил точек
          </div>
          <!-- /ko -->

          <!-- ko if: $data.hasPoints -->
          <div class="first-click-answers-stats-sidesheet-mobile-table__clicks">
            <!-- ko if: $data.clicksByArea.length > 0 -->
            <!-- ko foreach: { data: $data.clicksByArea, as: '$clickItem' } -->
            <div class="first-click-answers-stats-sidesheet-mobile-table__click-item">
              <div
                class="first-click-answers-stats-sidesheet-mobile-table__click-label"
                data-bind="text: $clickItem.areaName"
              ></div>
              <div
                class="first-click-answers-stats-sidesheet-mobile-table__click-time"
                data-bind="text: $clickItem.points.length + ' с'"
              ></div>
            </div>
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko if: $data.userDefinedPoints.length > 0 -->
            <!-- ko foreach: { data: $data.userDefinedPoints, as: '$clickItem' } -->
            <div class="first-click-answers-stats-sidesheet-mobile-table__click-item">
              <div
                class="first-click-answers-stats-sidesheet-mobile-table__click-label"
                data-bind="text: $clickItem.name"
              ></div>
              <div
                class="first-click-answers-stats-sidesheet-mobile-table__click-time"
                data-bind="text: $clickItem.timeInSeconds + ' с'"
              ></div>
            </div>
            <!-- /ko -->
            <!-- /ko -->
            <div class="first-click-answers-stats-sidesheet-mobile-table__map-link">
              <a
                href="#"
                class="first-click-answers-stats-sidesheet-mobile-table__link first-click-answers-stats-sidesheet-mobile-table__link--primary"
                data-bind="click: function() { $modal.openClicksMapSidesheet($data); }"
                >Карта кликов</a
              >
            </div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
