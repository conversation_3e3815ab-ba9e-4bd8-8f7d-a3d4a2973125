import { DialogWrapper } from 'Dialogs/wrapper';
import html from './template.html';
import './style.less';

class StatsHeatmapDetailsSidesheet extends DialogWrapper {
  constructor(params = {}, element) {
    console.log('StatsHeatmapDetailsSidesheet constructor called with:', { params, element });
    super(params, element);
    
    this.question = params.question;
    this.title = params.title || this.question.description || 'Тепловая карта кликов';
    this.imageSrc = ko.observable(ko.unwrap(this.question.imageSrc) || ko.unwrap(this.question.imageUrl));

    this.processedAreas = ko.observableArray(this.question.processedAreas.map(area => ({
      ...area,
      visible: ko.observable(true)
    })));

    this.allAreasVisible = ko.pureComputed({
      read: () => this.processedAreas().every(area => area.visible()),
      write: (value) => {
        this.processedAreas().forEach(area => area.visible(value));
        this.updateHeatmapOverlay();
      }
    });
    
    this.heatmapData = ko.computed(() => {
      return this.generateHeatmapData();
    });

    this.heatmapOptions = {
      gridWidth: this.question.gridWidth,
      gridHeight: this.question.gridHeight,
    };
  }

  onModalInit(...params) {
    super.onModalInit(...params);
  }

  toggleAreaVisibility(area) {
    area.visible(!area.visible());
    this.updateHeatmapOverlay();
  }
  
  get visibleAreas() {
    return this.processedAreas().filter(area => area.visible());
  }

  generateHeatmapData() {
    const heatmapData = this.question.heatmapData || [];
    console.log('[SIDESHEET HEATMAP] Using real heatmap data:', heatmapData.length, 'points');
    
    if (heatmapData.length === 0) {
      console.log('[SIDESHEET HEATMAP] No heatmap data available');
      return [];
    }
    
    console.log('[SIDESHEET HEATMAP] Heatmap data sample:', heatmapData.slice(0, 3));
    return heatmapData;
  }


  updateHeatmapOverlay() {
  }
}

ko.components.register('stats-heatmap-details-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('stats-heatmap-details-sidesheet');

      return new StatsHeatmapDetailsSidesheet(params, element);
    },
  },
  template: html,
});

export default StatsHeatmapDetailsSidesheet;
