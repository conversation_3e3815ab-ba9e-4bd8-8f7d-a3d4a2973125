
.first-click-heatmap-sidesheet {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
  }

  &__title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  &__close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  &__body {
    padding: 20px;
    min-height: 0;
  }

  &__filters {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .first-click-stats__checkbox {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin: 0;
    }
    .first-click-stats__checkbox-input {
        margin-right: 8px;
    }
    .first-click-stats__checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        &--with-indicator {
            gap: 8px;
        }
    }
    .first-click-stats__color-indicator {
        width: 16px;
        height: 16px;
        border-radius: 4px;
        flex-shrink: 0;
    }
  }

  &__container {
    display: flex;
    flex-direction: column;
    min-height: 0;
    min-width: 0;
    margin-right: 0;
    margin-bottom: 30px;

    @media screen and (max-width: 1199px) {
      padding-right: 30px;
      padding-left: 20px;
    }

    @media screen and (max-width: 768px) {
      padding-right: 0 !important;
      padding-left: 0 !important;
      padding-bottom: 30px !important;
      margin-bottom: 0 !important;
    }
  }

  &__container-inner {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .os-scrollbar-horizontal {
      bottom: -26px;
    }
    .os-scrollbar-vertical {
      right: -12px;
    }
    .os-scrollbar-vertical, .os-scrollbar-horizontal {
      z-index: 10;
    }
    &.os-host-overflow {
      overflow: visible !important;
    }
    @media screen and (max-width: 768px) {
      .os-scrollbar-horizontal {
        display: none !important;
      }
      .os-scrollbar-vertical {
        display: none !important;
      }
    }
  }

  .heatmap-chart {
    &__container {
      width: auto;
      height: auto;
    }
    &__background-image {
      width: auto;
      height: auto;
      max-width: none;
      max-height: none;
    }
  }

  &__image-wrapper {
    position: relative;
    display: inline-block;
    border: 1px solid #ddd;
  }

  &__image {
    max-width: 100%;
    height: auto;
    display: block;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__areas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__area {
    position: absolute;
    border: 2px solid #3F51B5;
    background-color: rgba(63, 81, 181, 0.2);
    transition: all 0.2s ease;
    box-sizing: border-box;
  }

  &__area-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    color: #333;
  }
}
