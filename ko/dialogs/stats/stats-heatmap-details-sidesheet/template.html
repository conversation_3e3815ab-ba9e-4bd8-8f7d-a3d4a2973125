<!-- ko let: { $statsDialog: $component } -->
<sidesheet class="first-click-heatmap-sidesheet" params="ref: modal, dialogWrapper: $component">
  <div
    class="foquz-dialog__body pt-4 pb-4"
    style="display: flex; flex-direction: column; height: 100%; overflow: hidden"
  >
    <!-- Fixed Header -->
    <div class="container flex-shrink-0">
      <!-- Container for header's horizontal padding/max-width -->
      <div class="stats-sidesheet__header mb-25p">
        <div class="px-2">
          <h2 class="foquz-dialog__title" data-bind="text: title"></h2>
        </div>
      </div>
    </div>

    <!-- Scrollable Area for Heatmap -->
    <div class="container first-click-heatmap-sidesheet__container">
      <div class="first-click-heatmap-sidesheet__container-inner" data-bind="nativeScrollbar">
        <div class="foquz-dialog__scroll">
          <!-- Heatmap container -->
          <div class="first-click-heatmap-sidesheet__image-wrapper">
            <!-- Heatmap Chart Component -->
            <heatmap-chart
              params="
              imageSrc: imageSrc,
              data: heatmapData,
              options: heatmapOptions
              "
            ></heatmap-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
