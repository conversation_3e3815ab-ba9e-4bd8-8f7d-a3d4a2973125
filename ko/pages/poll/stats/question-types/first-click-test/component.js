// ko/pages/poll/stats/question-types/first-click-test/component.js


import { QuestionStats } from '../models/component';
import HighchartsModern from '@/utils/highcharts-modern';

class ViewModel extends QuestionStats {
  constructor(params) {
    super({
      name: 'first-click-test',
      defaultChart: 'column',
      charts: ['heatmap', 'column'],
      question: params.question,
    });

    this.imageSrc = ko.observable(ko.unwrap(this.question.imageSrc) || ko.unwrap(this.question.imageUrl));
    
    const areas = this.question.processedAreas || [];
    console.log('[FIRST CLICK TEST COMPONENT] processedAreas from question:', areas);
    this.processedAreas = ko.observableArray(
      Array.isArray(areas) ? areas.map(area => ({
        ...area,
        visible: ko.observable(true),
        enhanced: ko.observable(true),
        opacity: ko.observable(0.8),
        borderWidth: ko.observable(5)
      })) : []
    );
    console.log('[FIRST CLICK TEST COMPONENT] initialized processedAreas:', this.processedAreas());

    this.heatmapData = ko.computed(() => {
      return this.generateHeatmapData();
    });

    this.heatmapOptions = {
      gridWidth: this.question.gridWidth,
      gridHeight: this.question.gridHeight,
    };

    this.showAreas = ko.observable(false);

    this.heatmapChartHeight = ko.observable(0);
    this.heatmapExpanded = ko.observable(false);
    this.heatmapResizeObserver = null;

    this.mode = ko.observable('column');

    this.allAreasVisible = ko.pureComputed({
        read: () => this.processedAreas().every(area => area.visible()),
        write: (value) => {
            this.processedAreas().forEach(area => area.visible(value));
        }
    });

    this.mode.subscribe((newMode) => {
      setTimeout(() => {
        if (newMode === 'column') {
          this.resetContainerHeight();
          this.initColumnChart();
        } else if (newMode === 'heatmap') {
          this.updateHeatmapHeight();
        }
      }, 100);
    });
  }

  generateHeatmapData() {
    const heatmapData = this.question.heatmapData || [];
    console.log('[HEATMAP] Using real heatmap data:', heatmapData.length, 'points');
    
    if (heatmapData.length === 0) {
      console.log('[HEATMAP] No heatmap data available');
      return [];
    }
    
    console.log('[HEATMAP] Heatmap data sample:', heatmapData.slice(0, 3));
    return heatmapData;
  }

  toggleAreas() {
    this.showAreas(!this.showAreas());
  }

  toggleAreaVisibility(area) {
    area.visible(!area.visible());
  }
  
  get visibleAreas() {
    return this.processedAreas().filter(area => area.visible());
  }

  openAreaDetails(area) {
    this.question.openAreaDetails(area);
  }

  openUserPointsDetails() {
    this.question.openUserPointsDetails();
  }

  openSkippedDetails() {
    this.question.openSkippedDetails();
  }
  
  openHeatmapDetailsModal() {
    this.question.openHeatmapDetailsModal();
  }

  onAllAnswersClick() {
    this.question.onAllAnswersClick();
  }

  onHeatmapPointClick(point) {
    console.log('[FIRST CLICK TEST] Heatmap point clicked:', point);
  }

  openHeatmapDetails() {
    this.question.openHeatmapDetails();
  }

  updateHeatmapHeight(){
    setTimeout(() => {
      this.setupHeatmapResizeObserver();
    }, 300);
  }

  setupHeatmapResizeObserver() {
    if (this.heatmapResizeObserver) {
      this.heatmapResizeObserver.disconnect();
      this.heatmapResizeObserver = null;
    }

    const heatmapInnerElement = document.querySelector('.first-click-stats__heatmap-chart-wrapper-inner');
    console.log('[FIRST CLICK TEST] Heatmap inner element:', heatmapInnerElement);
    if (heatmapInnerElement && window.ResizeObserver) {
      this.heatmapResizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const height = entry.contentRect.height;
          this.heatmapChartHeight(height);
          console.log('[FIRST CLICK TEST] Heatmap height updated via ResizeObserver:', height);
        }
      });

      this.heatmapResizeObserver.observe(heatmapInnerElement);
      
      const initialHeight = heatmapInnerElement.offsetHeight;
      this.heatmapChartHeight(initialHeight);
      console.log('[FIRST CLICK TEST] Initial heatmap height:', initialHeight);
    } else {
      this.fallbackHeightTracking();
    }
  }

  fallbackHeightTracking() {
    const heatmapInnerElement = document.querySelector('.first-click-stats__heatmap-chart-wrapper-inner');
    if (heatmapInnerElement) {
      const height = heatmapInnerElement.offsetHeight;
      this.heatmapChartHeight(height);
      console.log('[FIRST CLICK TEST] Fallback heatmap height:', height);
    }
  }

  resetContainerHeight() {
    const columnContainerId = this.chartIds.column;
    const columnContainer = document.getElementById(columnContainerId);
    if (columnContainer) {
      columnContainer.style.height = 'auto';
    }
    
    if (this.charts.column) {
      this.charts.column.destroy();
      this.charts.column = null;
    }
  }

  initColumnChart() {
    return super.initColumnChart({
      tooltip: {
        headerFormat: undefined,
      },
    });
  }

  onInit() {
    if (this.mode() === 'column') {
        this.initColumnChart();
    } else if (this.mode() === 'heatmap') {
        this.updateHeatmapHeight();
    }
  }

  makeAreasMoreVisible() {
    this.processedAreas().forEach(area => {
      area.opacity(0.9);
      area.borderWidth(6);
      area.enhanced(true);
    });
  }

  resetAreaVisibility() {
    this.processedAreas().forEach(area => {
      area.opacity(0.8);
      area.borderWidth(5);
      area.enhanced(false);
    });
  }

  // Cleanup method
  dispose() {
    // Clean up ResizeObserver
    if (this.heatmapResizeObserver) {
      this.heatmapResizeObserver.disconnect();
      this.heatmapResizeObserver = null;
    }
    
    super.dispose && super.dispose();
  }
}

// Register the component
ko.components.register('first-click-test-question-stats', {
  viewModel: ViewModel,
  template: {
    element: 'first-click-test-question-stats-template'
  }
});
