import { declOfNum } from 'Utils/string/decl-of-num';
import { Question } from "../models/question";
import "./component";
import "./style.less";

export class FirstClickTestQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    [
      "statistics",
      "clickAreas",
      "imageUrl",
      "imageWidth",
      "imageHeight",
      "gallery",
      "name", 
      "question_id",
    ].forEach((key) => {
      this[key] = questionData[key];
    });

    this.imageSrc = ko.pureComputed(() => {
      console.log('[FIRST CLICK TEST] gallery', this.gallery);
      if (this.gallery) {
        return this.gallery[0].src;
      }
      return null;
    });

    console.log('[FIRST CLICK TEST] statistics', this.statistics);
    console.log('[FIRST CLICK TEST] clickAreas', this.clickAreas);
    console.log('[FIRST CLICK TEST] imageUrl', this.imageUrl);
    console.log('[FIRST CLICK TEST] imageWidth', this.imageWidth);
    console.log('[FIRST CLICK TEST] imageHeight', this.imageHeight);

    this.totalClicks = this.statistics.clicks_count || 0;

    this.totalClicksDeclOfNum = declOfNum(this.totalClicks, ['клик', 'клика', 'кликов']);

    this.avgExecutionTime = Number.parseInt(this.statistics.time_click_avg) || 0;

    this.skipped = this.statistics.skipped || 0;
    this.imageSize = this.statistics.imageSize || {};
    this.imageWidth = this.imageSize.w || 0;
    this.imageHeight = this.imageSize.h || 0;
    this.gridWidth = this.statistics.gridWidth || 10;
    this.gridHeight = this.statistics.gridHeight || 10;
    this.heatmapData = this.statistics.heatmap || [];

    this.processedAreas = this.processClickAreas();

    this.userDefinedPointsClicks = this.statistics?.points?.['0']?.length || 0;
    this.userDefinedPointsPercent = this.totalClicks > 0 
      ? parseFloat((this.userDefinedPointsClicks / this.totalClicks * 100).toFixed(1)).toString()
      : '0';

    this.selectedArea = ko.observable(null);
    this.showHeatmap = ko.observable(true);
  }

  filterHeatmapData(heatmap) {
    return heatmap.filter((d) => d.value > 0);
  }

  fillHeatmapData(heatmap) {
    if (!heatmap) {
      return [];
    }
    const gridSize = 10;
    const heatmapMap = new Map();

    heatmap.forEach(p => {
      if (p.x < gridSize && p.y < gridSize) {
        heatmapMap.set(`${p.x},${p.y}`, p.value);
      }
    });

    const filledHeatmap = [];
    for (let y = 0; y < gridSize; y++) {
      for (let x = 0; x < gridSize; x++) {
        const key = `${x},${y}`;
        const value = heatmapMap.has(key) ? heatmapMap.get(key) : 0;
        filledHeatmap.push({ x, y, value });
      }
    }

    return filledHeatmap;
  }

  processClickAreas() {
    if (!this.clickAreas || !Array.isArray(this.clickAreas)) {
      console.warn('No click areas defined for question', this.id);
      return [];
    }
    
    return this.clickAreas.map((area, index) => {
      try {
        const areaClicks = this.statistics?.points?.[area.id]?.length || 0;
        return {
          ...area,
          clicks: areaClicks,
          percentage: this.totalClicks > 0
            ? parseFloat((areaClicks / this.totalClicks * 100).toFixed(1)).toString()
            : 0,
          color: this.colors[index % this.colors.length]
        };
      } catch (error) {
        console.error('Error processing area', area, error);
        return null;
      }
    }).filter(Boolean);
  }

  get skippedPercent() {
    if (this.totalCount === 0) return 0;
    const percent = ((this.skipped / this.totalCount) * 100).toFixed(1);
    return parseFloat(percent).toString();
  }

  get columnChartData() {
    const data = this.processedAreas.map((area) => ({
      name: area.name,
      data: [area.clicks],
      color: area.color
    }));

    if (this.userDefinedPointsClicks > 0) {
      data.push({
        name: 'Пользовательские точки',
        data: [this.userDefinedPointsClicks],
        color: '#F96261'
      });
    }
    if (this.skipped > 0) {
      data.push({
        name: 'Респондент отказался от ответа',
        data: [this.skipped],
        color: '#B0BEC5'
      });
    }

    return data;
  }

  get colors() {
    return [
      "#3F51B5", "#536DFE", "#82B1FF", "#84ffff",
      "#aadbff", "#bdb2ff", "#ff9dd8", "#ffbdb4"
    ];
  }

  get totalCount() {
    let total = this.totalClicks;
    return total || 0;
  }

  openAreaDetails(area) {
    console.log('[FIRST CLICK TEST] openAreaDetails', area); 
    this.ctx.openStatsModal("stats-first-click-all-answers-sidesheet", {
      question: this,
      title: `${area.name}`,
      view: 'area',
      filterParams: { area_id: area.id },
      urlParams: {
        useSearchClients: true,
        field: 'first_click_area',
        value: String(area.id)
      }
    });
  }

  openUserPointsDetails() {
    this.ctx.openSidesheet("stats-first-click-all-answers-sidesheet", {
      question: this,
      title: "Пользовательские точки",
      filterParams: { filter_type: 'user_points' },
      view: 'user_points',
      urlParams: {
        useSearchClients: true,
        field: 'first_click_area',
        value: '0'
      }
    });
  }

  openSkippedDetails() {
    console.log('[FIRST CLICK TEST] openSkippedDetails');
    this.ctx.openStatsModal("stats-first-click-all-answers-sidesheet", {
      question: this,
      title: "Клиенты с пропуском ответа",
      filterParams: { filter_type: 'skipped' },
      view: 'skipped',
      urlParams: {
        useSearchClients: true,
        field: 'skipped',
        value: '1'
      }
    });
  }

  openAllAnswersDetails() {
    this.ctx.openStatsModal("stats-first-click-all-answers-sidesheet", {
      question: this,
      title: this.name,
      filterParams: { filter_type: 'all' }
    });
  }
  
  openHeatmapDetailsModal() {
    this.ctx.openStatsModal("stats-heatmap-details-sidesheet", {
      question: this,
      title: this.name,
    });
  }

  openUserClickMapSidesheet(userClickPoints) {
    this.ctx.openStatsModal("stats-user-click-areas-sidesheet", {
      question: this,
      title: this.name,
      answer: {click_points: userClickPoints},
      imageSrc: ko.unwrap(this.imageSrc),
    });
  }

  onAllAnswersClick() {
    this.openAllAnswersDetails();
  }
}
