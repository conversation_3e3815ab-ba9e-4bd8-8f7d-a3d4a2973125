@import "Style/colors";

.first-click-stats {
  &__summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 25px;
    width: 100%;
    &--heatmap {
      margin-top: 0;
      flex: 0 0 354px;
      width: auto;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 15px;
      flex-direction: column;
      .first-click-stats__metric-label {
        max-width: 100%;
        flex-grow: 1;
        text-align: left;
      }
    }
  }

  &__metric {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &__metric-label {
    font-size: 14px;
    line-height: 1;
    max-width: 112px;
  }

  &__metric-value {
    font-size: 30px;
    line-height: 1;
    font-weight: 700;
    flex: 0 0 auto;
  }

  &__mode-toggle {
    margin-top: 18px;
  }

  &__statistics {
    margin-top: 0 !important;
    gap: 40px;
  }

  &__column-chart {
    margin-top: 25px !important;
  }

  &__mode-btn {
    &--active {
    }
  }

  &__heatmap-view {
    margin: 20px 0;
  }

  &__heatmap-content {
    gap: 40px;
  }

  &__heatmap-chart-wrapper {
    position: relative;
    width: 100%;

    &--tall {
      max-height: calc(500px + 15px);
      overflow: hidden;
      position: relative;
      & + .first-click-stats__heatmap-content {
        margin-top: 0 !important;
      }
    }
  }

  &__heatmap-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(360deg, #ffffff 40%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
    z-index: 10;
  }

  &__show-image-btn {
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 20;
    white-space: nowrap;
    color: @f-color-primary;
    background-color: transparent;
    border: none;
    padding: 27px 0;
    font-size: 13px;
    cursor: pointer;
    transition: opacity 0.2s ease;
    width: 100%;
    text-align: center;
    font-weight: 500;
    &:hover {
      opacity: 0.8;
    }
  }

  &__image-wrapper {
    position: relative;
    display: inline-block;
    max-width: 100%;
    width: 100%;

    &--limited {
      max-height: 400px;
      overflow: hidden;
      border: 1px solid #eee;
    }
  }

  &__image {
    max-width: 100%;
    height: auto;
    display: block;
    pointer-events: none;
  }

  &__heatmap-overlay {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    width: 100%;
    height: 100%;
    // pointer-events: none;
  }

  &__areas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__area {
    position: absolute;
    border: 2px solid #3f51b5;
    background-color: rgba(63, 81, 181, 0.2);
    cursor: pointer;
    pointer-events: all;
    transition: all 0.2s ease;
    box-sizing: border-box;

    &:hover {
      background-color: rgba(63, 81, 181, 0.4);
      border-color: #303f9f;
      z-index: 10;
    }
  }

  &__area-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: #333;
  }

  &__controls {
    display: flex;
    align-items: center;
    margin-top: 15px;
    gap: 10px;
  }

  &__time-chart,
  &__zones-chart {
    margin: 20px 0;
  }

  &__chart-container {
    min-height: 300px;
  }

  &__filter-table {
    &--heatmap {
      .bg-white {
        box-shadow: none !important;
      }
    }
  }

  &__table {
    width: 100%;
  }

  &__table-head {
  }

  &__table-header {
  }

  &__table-row {
  }

  &__table-cell {
    &--checkbox {
      font-weight: 500;
    }
  }

  &__checkbox {
    font-weight: 500;
  }

  &__checkbox-input {
    margin-right: 8px;
  }

  &__checkbox-label {
  }

  &__legend {
  }

  &__legend-table {
  }

  &__legend-head {
  }

  &__legend-header {
  }

  &__legend-row {
    &--clickable {
      cursor: pointer;
    }
  }

  &__legend-cell {
  }

  &__legend-item {
  }

  &__legend-text {
  }

  &__color-indicator {
    &--user-points {
      background-color: #f96261;
    }
  }

  &__header-controls {
  }

  &__visualization {
  }

  &__chart-wrapper {
    flex: 0 0 auto;
    &.first-click-stats__chart-wrapper--heatmap {
      max-width: 100% !important;
      width: 100% !important;
    }
  }

  &__all-answers-btn {
    cursor: pointer;
  }

  @media (max-width: 1200px) {
    &__summary {
      &.first-click-stats__summary--heatmap {
        flex: 0 0 254px;
      }
    }

    &__statistics {
      flex-direction: column;
      gap: 25px;
    }

    &__chart-wrapper {
      width: 394px !important;
    }
    &__legend {
      margin-top: 0 !important;
      width: 100% !important;
    }

    &__all-answers-btn {
      margin-top: 16px !important;
    }

    &__metric-value {
      line-height: 1;
    }

    &__heatmap-content {
      flex-direction: column;
      gap: 25px;
    }

    &__summary {
      &.first-click-stats__summary--heatmap {
        flex: 0 0 auto;
        width: auto;
        margin-right: 0 !important;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
        .first-click-stats__metric-label {
          max-width: 112px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    &__heatmap-content {
      flex-direction: column;
    }

    &__summary {
      flex: 0 0 100%;
      width: 100%;
      margin-right: 0 !important;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 15px;
      margin-top: 25px !important;
      &.first-click-stats__summary--heatmap {
        margin-top: 0 !important;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 15px;
        .first-click-stats__metric {
          max-width: none;
        }
      }
    }

    &__chart-wrapper {
      width: 100% !important;
      &--heatmap {
        margin-bottom: 16px;
      }
    }

    &__metric-label {
      max-width: none !important;
    }

    &__legend.question-statistics__variant-statistics-legend {
      margin-top: 0 !important;
      width: calc(100% + 30px) !important;
    }

    &__heatmap-view {
      margin-bottom: 0 !important;
    }

    &__header-controls {
    }

    &__controls {
      flex-direction: column;
      align-items: stretch;
    }

    &__mode-toggle {
      justify-content: center;
      margin-top: 22px;
    }

    &__area-label {
    }

    &__filter-table {
      margin-left: -15px;
      margin-right: -15px;
      width: calc(100% + 30px) !important;

      .first-click-stats__table {
        margin-bottom: 0 !important;
      }
    }

    &__table-header,
    &__table-cell,
    &__legend-header,
    &__legend-cell {
    }
  }
}
