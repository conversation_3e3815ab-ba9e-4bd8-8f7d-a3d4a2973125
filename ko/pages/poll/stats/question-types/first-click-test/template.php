<!-- ko/pages/poll/stats/question-types/first-click-test/template.php -->

<template id="first-click-test-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->

  <div class="poll-stats poll-stats--first-click-test first-click-stats">
    <div class="d-flex justify-content-between align-items-center">
      <div class="question-statistics__variant-statistics-mode-toggle first-click-stats__mode-toggle">
        <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-column-mode-button first-click-stats__mode-btn"
                title="Столбчатая диаграмма"
                data-bind="click: function() { mode('column'); },
                            css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'column' }, tooltip">
        </button>
        <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-heatmap-mode-button first-click-stats__mode-btn"
                title="Тепловая карта"
                data-bind="click: function() { mode('heatmap'); },
                            css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'heatmap' }, tooltip">
        </button>
      </div>
    </div>

    <div class="question-statistics__question-variant-statistics question-statistics__variant-statistics first-click-stats__statistics">
      <div class="question-statistics__variant-statistics-chart-wrapper first-click-stats__chart-wrapper"
        data-bind="css: { 'first-click-stats__chart-wrapper--heatmap': mode() === 'heatmap' }">

        <!-- Column Chart -->
        <div class="question-statistics__variant-statistics-chart question-statistics__variant-statistics-column-chart first-click-stats__column-chart"
             data-bind="slide: mode() === 'column', attr: { id: chartIds.column }">
        </div>

        <!-- Heatmap View -->
        <div class="first-click-stats__heatmap-view" data-bind="slide: mode() === 'heatmap'">
          <!-- Heatmap Chart Wrapper -->
          <div class="first-click-stats__heatmap-chart-wrapper" 
               data-bind="
               css: { 'first-click-stats__heatmap-chart-wrapper--tall': heatmapChartHeight() >= 500 && !heatmapExpanded() },
               attr: { id: chartIds.heatmap }
               ">
            <div class="first-click-stats__heatmap-chart-wrapper-inner">
              <!-- Heatmap Chart Component -->
              <heatmap-chart params="
                imageSrc: imageSrc,
                data: heatmapData,
                options: heatmapOptions,
                onPointClick: onHeatmapPointClick.bind($data)
              "></heatmap-chart>

              <!-- ko if: heatmapChartHeight() >= 500 -->
              <div class="first-click-stats__heatmap-gradient"></div>
              <button class="first-click-stats__show-image-btn" 
                      data-bind="click: openHeatmapDetailsModal">
                Показать изображение
              </button>
              <!-- /ko -->
            </div>
          </div>

          <!-- Content below heatmap: Summary on left, Table on right -->
          <div class="d-flex mt-25p first-click-stats__heatmap-content">
            <!-- Summary Statistics - Left side -->
            <div class="first-click-stats__summary first-click-stats__summary--heatmap">
              <div class="first-click-stats__metric">
                <span class="first-click-stats__metric-value" data-bind="text: question.totalClicks"></span>
                <span class="first-click-stats__metric-label" data-bind="text: question.totalClicksDeclOfNum"></span>
              </div>
              <div class="first-click-stats__metric">
                <span class="first-click-stats__metric-value" data-bind="text: question.avgExecutionTime"></span>
                <span class="first-click-stats__metric-label">среднее время выполнения, сек</span>
              </div>
            </div>

            <!-- Areas filter table - Right side -->
            <div class="first-click-stats__filter-table first-click-stats__filter-table--heatmap flex-grow-1">
              <table class="table foq-table first-click-stats__table mb-0">
                <thead class="position-sticky sticky-top bg-white">
                  <tr>
                    <th>
                      <fc-check params="
                        checked: allAreasVisible,
                        label: 'Область клика'
                      "></fc-check>
                    </th>
                    <th class="text-right">Кол-во кликов</th>
                    <th class="text-right">Процент</th>
                  </tr>
                </thead>
                <tbody>
                <!-- ko foreach: processedAreas -->
                  <tr class="first-click-stats__table-row">
                    <td class="first-click-stats__table-cell first-click-stats__table-cell--checkbox">
                      <fc-check params="
                        checked: visible,
                        label: name,
                        click: function() { $parent.toggleAreaVisibility($data); },
                        class: 'first-click-stats__checkbox'
                      "></fc-check>
                    </td>
                    <td align="right" class="first-click-stats__table-cell" data-bind="text: clicks"></td>
                    <td align="right" class="first-click-stats__table-cell" data-bind="text: percentage + '%'"></td>
                  </tr>
                <!-- /ko -->
                <!-- ko if: question.statistics.skipped > 0 -->
                <tr class="first-click-stats__table-row">
                  <td class="first-click-stats__table-cell first-click-stats__table-cell--checkbox">
                    <div class="first-click-stats__legend-item">
                      <div class="first-click-stats__color-indicator first-click-stats__color-indicator--skipped" data-bind="style: { backgroundColor: '#DADFE3' }"></div>
                      <span>Респондент отказался от ответа</span>
                    </div>
                  </td>
                  <td align="right" class="first-click-stats__table-cell" data-bind="text: question.statistics.skipped"></td>
                  <td align="right" class="first-click-stats__table-cell" data-bind="text: question.skippedPercent + '%'"></td>
                </tr>
                <!-- /ko -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Summary Statistics - moved below chart -->
        <!-- ko if: mode() === 'column' -->
        <div class="first-click-stats__summary mt-3">
          <div class="first-click-stats__metric">
            <span class="first-click-stats__metric-value" data-bind="text: question.totalClicks"></span>
            <span class="first-click-stats__metric-label" data-bind="text: question.totalClicksDeclOfNum"></span>
          </div>
          <div class="first-click-stats__metric">
            <span class="first-click-stats__metric-value" data-bind="text: question.avgExecutionTime"></span>
            <span class="first-click-stats__metric-label">среднее время выполнения, сек</span>
          </div>
        </div>
        <!-- /ko -->
      </div>

      <!-- Areas Table (Legend) - Only visible in column mode -->
      <!-- ko if: mode() === 'column' -->
      <div class="question-statistics__variant-statistics-legend poll-stats-legend variants-statistics-legend first-click-stats__legend" data-bind="nativeScrollbar">
        <table class="table foq-table question-statistics__variant-statistics-legend-table variants-statistics-legend-table first-click-stats__legend-table mb-0">
          <thead class="position-sticky sticky-top bg-white">
            <tr>
              <th>Область клика</th>
              <th class="text-right question-statistics__variant-statistics-legend-table-vote-count-head-cell">
                Кол-во кликов
              </th>
              <th class="text-right question-statistics__variant-statistics-legend-table-percentage-head-cell">
                Процент
              </th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: question.processedAreas --> <!-- Use question.processedAreas for consistency -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: function() { $parent.openAreaDetails($data); }">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator"
                       data-bind="style: { backgroundColor: color }">
                  </div>
                  <span data-bind="text: name"></span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: clicks"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: percentage + '%'"></td>
            </tr>
            <!-- /ko -->

            <!-- Пользовательские точки row -->
            <!-- ko if: question.userDefinedPointsClicks > 0 -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: openUserPointsDetails">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator first-click-stats__color-indicator--user-points"></div>
                  <span>Пользовательские точки</span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: question.userDefinedPointsClicks"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: question.userDefinedPointsPercent + '%'"></td>
            </tr>
            <!-- /ko -->

            <!-- Респондент отказался от ответа row -->
            <!-- ko if: question.statistics.skipped > 0 -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: openSkippedDetails">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator first-click-stats__color-indicator--skipped" data-bind="style: { backgroundColor: '#DADFE3' }"></div>
                  <span>Респондент отказался от ответа</span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: question.statistics.skipped"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: question.skippedPercent + '%'"></td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
      <!-- /ko -->
    </div>

    <!-- View all answers button -->
    <a class="no-print question-statistics__question-additional-button
      question-statistics__question-all-profiles-button first-click-stats__all-answers-btn"
       data-bind="click: onAllAnswersClick">
      Все ответы
    </a>
  </div>

  <!-- /ko -->
</template>
