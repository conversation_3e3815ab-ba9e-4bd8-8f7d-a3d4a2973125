<?php

namespace app\modules\foquz\models;

use Yii;
use yii\db\Query;
use function JmesPath\search;

/**
 * This is the model class for table "foquz_poll_answer_item_points".
 *
 * @property int $id
 * @property int $answer_item_id
 * @property int $x X-координата точки
 * @property int $y Y-координата точки
 * @property int $click_time Количество секунд от старта показа картинки до клика
 * @property int|null $area_id ID заданной области в которую попал клик
 *
 * @property FoquzPollAnswerItem $answerItem
 * @property FoquzQuestionFirstClickArea $area
 */
class FoquzPollAnswerItemPoints extends \yii\db\ActiveRecord
{

    /**
     * максимальное количество точек в тепловой карте
     */
    public const HEAT_MAP_MAX_POINTS = 9800;
    public static function saveAnswer(array $points, int $answerItemId, array|null $areas): void
    {
        self::deleteAll(['answer_item_id' => $answerItemId]);

        $batch = [];
        foreach($points as $point) {
            [$x, $y, $t] = explode(';', $point);
            $areaId = self::getAreaId($areas, [$x, $y]);
            $batch[] = [
                $answerItemId,
                $x,
                $y,
                $t,
                $areaId
            ];
        }
        \Yii::$app->db->createCommand()->batchInsert(
            self::tableName(),
            ['answer_item_id', 'x', 'y', 'click_time', 'area_id'],
            $batch
        )->execute();
    }

    private static function getAreaId(array $areas, array|null $point): int|null
    {
        foreach ($areas as $area) {
            if ($area->isAreaHasPoint($point)) {
                return $area->id;
            }
        }
        return null;
    }


    /**
     * Получить данные для тепловой карты
     * @param array $answerIds
     * @param int $imageWidth
     * @param int $imageHeight
     * @param int|null $areaId (null - все записи, 0 - только не попавшие ни в одну область, Id области - только записи для этой области )
     * @return array
     */
    public static function getHeatMap(array $answerIds, int $imageWidth, int $imageHeight, int $areaId = null): array
    {
        $divider = ($imageWidth * $imageHeight) / self::HEAT_MAP_MAX_POINTS;
        $divider = floor(sqrt($divider));

        $sx = floor($imageWidth / $divider);
        $sy = floor($imageHeight / $divider);

        $q = (new Query())
            ->from(self::tableName())
            ->select('count(*) as value, floor(x/'.$sx.') as x, floor(y/'.$sy.') as y')
            ->where(['in', 'answer_item_id', $answerIds])
            ->groupBy('floor(x/'.$sx.'), floor(y/'.$sy.')');
        if (!is_null($areaId)) {
            if ($areaId) {
                $q->where(['area_id' => $areaId]);
            } else {
                $q->where(['is','area_id', null]);
            }
        }

        $points = $q->all();

        $pointsLookup = [];
        foreach ($points as $point) {
            $pointsLookup[$point['x'] . ',' . $point['y']] = $point;
        }

        $allPoints = [];
        for ($x = 0; $x <= $sx; $x++) {
            for ($y = 0; $y <= $sy; $y++) {
                $coord = ['value' => 0, 'x' => $x, 'y' => $y];
                $key = $coord['x'] . ',' . $coord['y'];
                if (isset($pointsLookup[$key])) {
                    $coord = $pointsLookup[$key];
                }
                $allPoints[] = $coord;
            }
        }

        return ['heatmap' => $allPoints, 'gridWidth' => $sx, 'gridHeight' => $sy];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'foquz_poll_answer_item_points';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['answer_item_id', 'x', 'y', 'click_time'], 'required'],
            [['answer_item_id', 'x', 'y', 'click_time', 'area_id'], 'integer'],
            [['answer_item_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswerItem::class, 'targetAttribute' => ['answer_item_id' => 'id']],
            [['area_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestionFirstClickArea::class, 'targetAttribute' => ['area_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'answer_item_id' => 'Answer Item ID',
            'x' => 'X',
            'y' => 'Y',
            'click_time' => 'Click Time',
            'area_id' => 'Area ID',
        ];
    }

    /**
     * Gets query for [[AnswerItem]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAnswerItem()
    {
        return $this->hasOne(FoquzPollAnswerItem::class, ['id' => 'answer_item_id']);
    }

    /**
     * Gets query for [[Area]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getArea()
    {
        return $this->hasOne(FoquzQuestionFirstClickArea::class, ['id' => 'area_id']);
    }
}
